
import { getMacroEnvEcoDataList,getMacroEnvIndDataList } from '@/api/ambience/macro'

export const getInitDate = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldYear = year-1;
   let newmonth = month-1
  let oldMonth = month >=10 ? month :`0${month}`;

  if((month - 12)===0){
    oldMonth = `12`;
    oldYear = year
  }
  if(newmonth===0){
    newmonth = '12';
    year = year-1
  }

 return [ `${oldYear}-${oldMonth}`,`${year}-${newmonth}` ]
}

export const getInitDate2 = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldMonth = month >=10 ? month :`0${month}`

  year = month > 3 ? year : year - 1
  let oldYear = year-3;
  oldMonth = `01`;
  let newmonth = '12';

 return [ `${oldYear}-${oldMonth}`,`${year}-${newmonth}` ]
}



export const getInitYear = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldYear = year-1;
  let oldMonth = month >=10 ? month :`0${month}`

  let newmonth = month-1

  if((month - 12)===0){
    oldMonth = `01`;
    oldYear = year
  }
  if(newmonth===0){
    newmonth = '12';
    year = year-1
  }


 return [ `${oldYear}`,`${year}` ]
}


export const getInitDate3 = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;
  let oldMonth = month >=10 ? month :`0${month}`
  oldMonth = `01`;

  year = month > 3 ? year : year - 1
  month = month > 3 ? month : '12'
  let oldYear = year-3;

 return [ `${oldYear}-${oldMonth}`,`${year}-${month}` ]
}


export const getInityearNomonth = ()=>{
  const currentDate = new Date();
  let year = currentDate.getFullYear();
  let month = currentDate.getMonth() + 1;

  year = month > 3 ? year : year - 1
  let oldYear = year-3;

 return [ `${oldYear}`,`${year}` ]
}


const state = {
    // 经济环境图表数据
    economyChartList:[],
    // 经济环境入参
    economyParams:{
      date:getInitDate3()
    },
    // 加载图标
    economyLoading:false,
     // 加载图标
    estateLoading:false,
    // 产业环境数据
    estateChartList:[],
    // 产业环境入参
    estateParams:{
      date:getInitDate3()
    },
    

}
const mutations = {
  CHANGE_CHARTlIST: (state, { key, value }) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value
    }
  },
}

const actions = {
  // 获取经济环境
 async getChartList({commit,state}) {
    try {
      if(state.economyLoading)return
      state.economyLoading = true;
      commit('CHANGE_CHARTlIST', {
        key:'economyLoading',
        value:true
      })
      const params = state.economyParams
      const data = await getMacroEnvEcoDataList(params)
      commit('CHANGE_CHARTlIST', {
        key:'economyChartList',
        value:data
      })
      commit('CHANGE_CHARTlIST', {
        key:'economyLoading',
        value:false
      })
    } catch (error) {
      commit('CHANGE_CHARTlIST', {
        key:'economyLoading',
        value:false
      })
      console.log(error,'error')
    }
   
  },
  // 产业环境
  async getMacroEnvIndData({commit,state}) {
    try {
      if(state.estateLoading)return
      await commit('CHANGE_CHARTlIST', {
        key:'estateLoading',
        value:true
      })
      const params = state.estateParams
      const data = await getMacroEnvIndDataList(params)
      commit('CHANGE_CHARTlIST', {
        key:'estateChartList',
        value:data
      })
      commit('CHANGE_CHARTlIST', {
        key:'estateLoading',
        value:false
      })
    } catch (error) {
      console.log(error,'error')
      commit('CHANGE_CHARTlIST', {
        key:'estateLoading',
        value:false
      })
    }
   
  },
  
 
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

