import Tpis from '@/views/components/tooltip/index.vue'
import { numberFormat } from '@/utils/format'

// Tooltip组件渲染
 export const TooltipComponent = (propos) => {
  propos.params.sort((a, b) => {
    return a.seriesType == 'line' ? -1 : 1
  })
  return (
    <Tpis {...propos}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>{numberFormat(item.value,item.seriesType =='line' ? 1:0) || 0}{item.seriesType =='line'  ? '%' : '台'}</span>
            </>
          )
        }
      }}
    </Tpis>
  )
}

// Tooltip组件渲染
 export const TooltipPercentageComponent = (propos) => {
  propos.params.sort((a, b) => {
    return a.seriesType == 'line' ? -1 : 1
  })
  return (
    <Tpis {...propos}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>{numberFormat(item.value,1) || 0}%</span>
            </>
          )
        }
      }}
    </Tpis>
  )
}

// Tooltip组件渲染 销量与占比
export const TooltipSalesAndPercentageComponent = (propos) => {
  let prop = propos;
  let params = propos.params
  // 将所有包含"其他"的项移到最后
  const others = params.filter(item => item.seriesName && item.seriesName.includes('其它'));
  const notOthers = params.filter(item => !(item.seriesName && item.seriesName.includes('其它')));
  params = [...notOthers, ...others];
  prop.params = params;
  console.log('代码内容',params)
  
  return (
    <Tpis {...prop}>
      {{
        item: ({ item }) => {
          
          return (
            <>
              <span>
                {numberFormat(item.value,0) || 0} | {numberFormat(item.data.proportion,1) || 0}% 
              </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}

// Tooltip组件渲染销量，占比，同比
// Tooltip组件渲染销量，占比，同比
export const TooltipSalesProportionYoYComponent = (propos) => {
  propos.params.sort((a, b) => {
    return a.seriesType == 'line' ? -1 : 1
  })
  return (
    <Tpis {...propos}>
      {{
        item: ({ item }) => {
          return (
            <>
              <span>
                {/* {numberFormat(item.value, item.seriesType == 'line' ? 1 : 0) || 0}
                {item.seriesType == 'line' ? '%' : '台'} */}
                {item.data.sales} | {item.data.proportion} | {item.data.yoy}
              </span>
            </>
          )
        }
      }}
    </Tpis>
  )
}
