<template>
  <el-form :model="params" ref="queryRef" label-width="0" :inline="true" class="search-form">
    <el-row :gutter="16">
      <el-col :span="4" :xs="8" :sm="8" :md="3">
        <el-form-item prop="year">
          <el-date-picker
            v-model="params.year"
            type="year"
            value-format="YYYY"
            format="YYYY"
            :disabled-date="disabledFeatureDate"
            placeholder="年份"
            :clearable="false"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="4" :xs="8" :sm="8" :md="3">
        <el-form-item prop="pointerType">
          <el-select v-model="params.pointerType" placeholder="指标类型" style="width: 100%">
            <el-option
              v-for="item in dictsPointerType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <!-- <el-col :span="4" :xs="8" :sm="8" :md="3">
        <el-form-item v-if="params.pointerType === '2'" prop="month">
          <el-select v-model="params.month" placeholder="月累" style="width: 100%">
            <el-option
              v-for="item in newDictsMonthTotal"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else-if="params.pointerType === '1'" prop="quarter">
          <el-select v-model="params.quarter" placeholder="季度" style="width: 100%">
            <el-option
              v-for="item in newDictsQuarter"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else prop="month">
          <el-select v-model="params.month" placeholder="月度" style="width: 100%">
            <el-option
              v-for="item in newDictsMonth"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col> -->
      <DictsResource
        :form="params"
        :dicts="data.linkageData"
        :props="[
          {
            name: '数据来源',
            key: 'dataSource',
            disabled: true,
            // hide: true,
            clearable: true
          },
          {
            name: '板块',
            key: 'segment',
            disabled: true
          },
          {
            name: '细分市场一',
            key: 'subMarket1'
          },
          {
            name: '细分市场二',
            hide: true,
            key: 'subMarket2'
          }
        ]"
        :propsEngineFactory="{ name: '发动机厂', key: 'engineFactory', show: false }"
        :propsManuFacturer="{ name: '主机厂', key: 'manuFacturer', show: true }"
        :propsFuelType="{ name: '燃料', key: 'fuelType', show: true, type: 'A' }"
        :propsWeightMidLight="{
          name: '重中轻',
          key: 'weightMidLight',
          show: true,
          disabled: false
        }"
      />
      <!-- type 燃料类型（A-新能源 B-汽油） -->
      <el-col
        v-if="params.dataSource === '1'"
        :xs="8"
        :sm="8"
        :md="params.dataType.length > 2 ? 5 : 3"
      >
        <el-form-item prop="dataType">
          <el-select
            v-model="params.dataType"
            multiple
            placeholder="数据扩展"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in dictDataType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="8" :sm="8" :md="3">
        <el-form-item prop="province">
          <el-select v-model="params.province" placeholder="省" clearable style="width: 100%">
            <el-option
              v-for="item in systemDicts.sys_province"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="3">
        <el-form-item>
          <el-button type="primary" @click="toggleSearch">查询</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <el-segmented
    ref="segmentedRef"
    v-model="params.subMarketAndBreed"
    :options="defaultDictsSubMarketAndBreed"
    @change="toggleSearch('segmented')"
    class="segmented"
  >
    <template #default="scope">
      <div>{{ scope.item.label }}</div>
    </template>
  </el-segmented>
</template>

<script setup>
import DictsResource from '@/views/components/DictsResource.vue'
import { dictsPointerType, dictDataType } from '@/utils/common/dicts.js'
import useInnerData from '@/utils/hooks/innerData.js'

const store = useStore()

// 快捷查询按钮
const defaultDictsSubMarketAndBreed = [
  {
    label: '总体',
    value: '',
    key: 'subMarket'
  },
  {
    label: '卡车',
    value: '卡车',
    key: 'subMarket'
  },
  {
    label: '客车',
    value: '客车',
    key: 'subMarket'
  },
  {
    label: '牵引车',
    value: '牵引车',
    key: 'breed'
  },
  {
    label: '载货车',
    value: '载货车',
    key: 'breed'
  },
  {
    label: '专用车',
    value: '专用车',
    key: 'breed'
  },
  {
    label: '自卸车',
    value: '自卸车',
    key: 'breed'
  },
  {
    label: '皮卡',
    value: '皮卡',
    key: 'breed'
  }
]
const props = defineProps({
  params: {
    type: Object,
    required: true,
    default: () => ({
      pointerType: '', // 指标类型(0-月，2-月累，1-季度)
      year: '', // 年份
      segment: '', // 板块
      dataSource: '', // 数据来源
      province: '',
      manuFacturer: '', // 主机厂
      weightMidLight: '', // 重中轻
      fuelType: '',
      subMarket1: '', // 细分市场1
      breed: '',
      quarter: '',
      month: '',
      dataType: [],
      subMarketAndBreed: '' // subMarket和breed的选中值（前端展示用）
    })
  }
})
const systemDicts = computed(() => store.state.dicts.dicts)
// const { disabledFeatureDate } = formValidate()
const emit = defineEmits(['change'])
const data = reactive({
  linkageData: [] // 多级联动数据
})
const params = reactive({ ...toRaw(props.params) })
// 使用自定义 Hook 并传入 params 和 toggleSearch
const { initDateRange, innerdate, disabledFeatureDate } = useInnerData(params, toggleSearch)
watch(
  () => params.pointerType,
  val => {
    innerdate()
  }
)
// 监听年份变化
watch(
  () => params.year,
  val => {
    innerdate()
  }
)
// watch(
//   () => params.subMarketAndBreed,
//   val => {
//     const subMarket = ['总体', '卡车', '客车']
//     if (subMarket.findIndex(i => i === val) !== -1) {
//       params.subMarket1 = val
//       params.breed = ''
//     } else {
//       params.subMarket1 = ''
//       params.breed = val
//     }
//   },
//   { immediate: true }
// )
/**
 * @description 点击查询按钮获取参数，并触发emit事件传递搜索参数
 */
async function toggleSearch(ev) {
  if (ev === 'segmented') {
    const val = params.subMarketAndBreed
    const subMarket = ['', '卡车', '客车']
    if (subMarket.findIndex(i => i === val) !== -1) {
      params.subMarket1 = val
      params.breed = ''
    } else {
      params.subMarket1 = ''
      params.breed = val
    }
  } else {
    params.subMarketAndBreed = params.subMarket1
  }
  await nextTick()
  const data = toRaw(params)
  emit('change', data)
}

const getDictsData = async () => {
  const dicts = await store
    .dispatch('dicts/getDictsData', {
      keyArray: ['dataSource', 'segment', 'subMarket1', 'subMarket2'],
      dataSource: ['上险数']
    })
    .catch(e => e)
  if (dicts && dicts.length > 0) {
    data.linkageData = dicts
  }
}
initDateRange('上险数', true)
getDictsData()
</script>

<style scoped lang="scss">
.search-form {
  margin-bottom: 0;
}
.segmented {
  margin: 10px 0;
}
</style>
