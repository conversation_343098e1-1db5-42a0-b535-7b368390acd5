<template>
  <el-card>
    <template #header>
      <block-title :title="props.title" :icon="props.titleIcon" />
    </template>
    <div class="ratio-width" style="padding-bottom: 57%">
      <div ref="tableContain" class="ratio-width__wrap">
        <el-table
          ref="tableRef"
          highlight-current-row
          :data="props.list"
          class="table-box"
          height="100%"
          style="width: 100%"
          :border="true"
        >
          <el-table-column label="排名" width="46" align="center" class-name="bi-engine-center">
            <template #default="{ $index }">
              <img v-if="$index === 0" src="@/assets/images/no1.png" class="rank-icon" />
              <img v-else-if="$index === 1" src="@/assets/images/no2.png" class="rank-icon" />
              <img v-else-if="$index === 2" src="@/assets/images/no3.png" class="rank-icon" />
              <span v-else class="rank-txt">{{ $index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="engineManufacturer"
            label="企业"
            min-width="70"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column min-width="70">
            <template #default="{ row }">
              <el-progress
                :percentage="row.salesPercent"
                :color="row.color"
                :stroke-width="12"
                :show-text="false"
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column :label="`${data.pointerType == '0' ?'当月销量':'累计'}(台)`" min-width="70" align="right">
            <template #default="{ row }">
              {{ numberFormat(row?.sales, 0) }}
            </template>
          </el-table-column>
          <el-table-column label="同比" min-width="60" align="right">
            <template #default="{ row }">
              <span
                :style="{
                  color: row.salesContrast && row.salesContrast.includes('-') ? '#f00' : ''
                }"
                >{{ row.salesContrast }}</span
              >
            </template>
          </el-table-column>
          <el-table-column label="占有率" min-width="60" align="right">
            <template #default="{ row }">
              <span :style="{ color: row.slice && row.slice.includes('-') ? '#f00' : '' }">{{
                row.slice
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="占有率同比" min-width="70" align="right">
            <template #default="{ row }">
              <span
                :style="{
                  color: row.sliceContrast && row.sliceContrast.includes('-') ? '#f00' : ''
                }"
                >{{ row.sliceContrast }}</span
              >
            </template>
          </el-table-column>
          <el-table-column label="排名变化" min-width="50" align="right">
            <template #default="{ row }">
              <span v-if="+row.rankingContrast === 0 || isNaN(+row.rankingContrast)"></span>
              <span style="color: green" v-else-if="+row.rankingContrast > 0"
                >↑ {{ Math.abs(row.rankingContrast) }}</span
              >
              <span style="color: red" v-else="+row.rankingContrast < 0"
                >↓ {{ Math.abs(row.rankingContrast) }}</span
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import BlockTitle from '@/views/components/BlockTitle.vue'

import { numberFormat } from '@/utils/format.js'
import { nextTick, watch } from 'vue'
const props = defineProps({
  data:{
    type:Object,
    required: false,
    default: () => {}

  },
  list: {
    type: Array,
    required: false,
    default: () => []
  },
  title: {
    type: String,
    required: false,
    default: ''
  }
})

const tableRef = ref(null)
watch(
  () => props.list,
  nVal => {
    nVal.forEach(v => {
      if (v.engineManufacturer.includes('玉柴')) {
        tableRef && tableRef.value.setCurrentRow(v)
      }
    })
  }
)
</script>
<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';
.table-box {
  :deep(.cell) {
    // display: flex;
    // align-items: center;
    // justify-content: center;
    line-height: 38px;
    padding: 0 10px;
  }
  :deep(.el-table__cell) {
    padding: 0;
    line-height: 38px;
  }
}

:deep(.el-table th.el-table__cell) {
  background-color: #fff;
  color: var(--el-text-color-regular);
  font-weight: bold;
  font-size: 14px;
}

:deep(.el-progress-bar__inner) {
  border-radius: 2px;
}
:deep(.el-progress-bar__outer) {
  border-radius: 2px;
}
.rank-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}
.rank-txt {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}
</style>
