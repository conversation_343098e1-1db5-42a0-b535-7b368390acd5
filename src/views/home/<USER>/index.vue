<template>
  <div class="wrap">
    <SearchFormResource :params="data.params" @change="getParams" />

    <el-row :gutter="16">
      <el-col :xs="24" :sm="24" :md="15">
        <div class="map-wrap bi-loading-mask" v-loading="loading.chartData">
          <div class="sales">
            <div class="sales__item">
              <div class="sales__item--name">
                总销量{{ data.mapTotal.dateRange ? `（${data.mapTotal.dateRange}）` : '' }}
              </div>
              <div
                class="sales__item--value"
                :style="{
                  color: data.mapTotal.totalSales.toString().includes('-') ? '#F16C55' : '#115E93'
                }"
              >
                {{ data.mapTotal.totalSales ? numberFormat(data.mapTotal.totalSales, 0) : '—' }}
              </div>
            </div>
            <div class="sales__item">
              <div class="sales__item--name">同比增长</div>
              <div
                class="sales__item--value"
                :style="{
                  color: data.mapTotal.totalProp.toString().includes('-') ? '#F16C55' : '#115E93'
                }"
              >
                {{ data.mapTotal.totalProp ? numberFormat(data.mapTotal.totalProp) : '—' }}
              </div>
            </div>
          </div>
          <maps ref="refMaps" :series-data="data.chartData" @select="changeSelectArea" />
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :md="9">
        <el-card style="margin-bottom: 12px">
          <div class="ratio-width" style="padding-bottom: 58.5%">
            <div ref="target" class="ratio-width__wrap">
              <el-table
                stripe
                v-loading="loading.tableA"
                :data="data.tableA"
                class="table-box bi-loading-mask"
                height="100%"
                style="width: 100%"
                :border="true"
                
              >
                <el-table-column
                  prop="submarket"
                  label="细分市场"
                  min-width="60"
                  show-overflow-tooltip
                  align="center"
                />
                <el-table-column
                  align="right"
                  prop="sales"
                  label="销量(辆)"
                  min-width="60"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    {{ numberFormat(row.sales, 0) }}
                  </template>
                </el-table-column>
                <el-table-column align="right" label="同比" min-width="60" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.sales_prop ? (row.sales_prop.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.sales_prop ? numberFormat(row.sales_prop) : '/' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column align="right" label="占有率" min-width="60" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.proportion ? (row.proportion.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.proportion ? numberFormat(row.proportion) : '/' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  align="right"
                  label="占有率同比"
                  min-width="70"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.prop_change ? (row.prop_change.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.prop_change ? numberFormat(row.prop_change) : '/' }}</span
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
        <el-card>
          <div class="ratio-width" style="padding-bottom: 58.5%">
            <div ref="target" class="ratio-width__wrap">
              <el-table
                stripe
                v-loading="loading.tableB"
                :data="data.tableB"
                class="table-box"
                height="100%"
                style="width: 100%"
                :border="true"
                 :row-style="rowStyle"
              >
                <el-table-column
                  min-width="70"
                  label="细分区域"
                  show-overflow-tooltip
                  align="center"
                >
                  <template #default="{ row }">
                    <div class="area-item" @click="toggleAreaItem(row.area)">{{ row.area }}</div>
                  </template>
                </el-table-column>
                <el-table-column
                  align="right"
                  prop="sales"
                  label="销量(辆)"
                  min-width="60"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    {{ numberFormat(row.sales, 0) }}
                  </template>
                </el-table-column>
                <el-table-column align="right" label="同比" min-width="60" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.sales_prop ? (row.sales_prop.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.sales_prop ? numberFormat(row.sales_prop) : '/' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column align="right" label="占有率" min-width="60" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.proportion ? (row.proportion.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.proportion ? numberFormat(row.proportion) : '/' }}</span
                    >
                  </template>
                </el-table-column>
                <el-table-column
                  align="right"
                  label="占有率同比"
                  min-width="70"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <span
                      :style="{
                        color: row.prop_change ? (row.prop_change.includes('-') ? '#f00' : '') : ''
                      }"
                      >{{ row.prop_change ? numberFormat(row.prop_change) : '/' }}</span
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { numberFormat } from '../../../utils/format'
import SearchFormResource from './components/SearchFormResource.vue'
import maps from './components/maps.vue'

import { exportMarket, exportMarketDateRange } from '@/api/intelligence/homePage.js'
const store = useStore()
const dataSource = store.state.dicts.dictsDataSource.find(v => v.label === '海关数据')?.value

const { proxy } = getCurrentInstance()
// 当前是否是地区
const isarea = ref(true)
// 初始化搜索条件
const originParams = {
  year: (new Date().getFullYear() - 1).toString(), // 年份
  month: '12', // 月
  pointerType: '2', // 指标类型(0-月，2-月累，1-季度)
  quarter: '1', // 季度
  segment: '商用车', // 板块
  subMarket1: '', // 细分市场1
  subMarket2: '', //  细分市场2
  manuFacturer: '', // 主机厂
  dataSource: dataSource, // 数据来源（海关数）
  area: '', // 区域
  finalDestination: '' //国家
  // weightMidLight: ''
}
const refMaps = ref(null)
const data = reactive({
  params: { ...originParams },
  mapTotal: { totalProp: '', totalSales: '', dateRange: '' },
  chartData: [],
  tableA: [], // 第一个列表
  tableB: [] // 第二个列表
})

const loading = reactive({
  chartData: false,
  tableA: false,
  tableB: false
})

/**
 * @description 点击搜索
 * @param params 搜索参数
 */
function getParams(params) {
  data.params = params
  isarea.value = true;
  initChartData(params)
}

function getSubMarketList(params) {
  exportMarket(params, 'subMarketList')
    .then(res => {
      if (res.code == 200) {
        const subMarketList = res.data.subMarketList
        data.tableA = subMarketList
        console.log('subMarketList', subMarketList)
      }
      loading.tableA = false
    })
    .catch(e => {
      loading.tableA = false
    })
}

/**
 * @description 处理接口数据
 * @param params 搜索参数
 */
const initChartData = params => {
  if (loading.chartData || loading.tableA || loading.tableB) {
    proxy.$modal.msgError('数据正在处理，请勿重复提交')
    return
  }
  if (data.params.area === '') {
    data.chartData = []
  }
  data.tableA = [] // 第一个列表
  data.tableB = [] // 第二个列表

  loading.chartData = true
  loading.tableA = true
  loading.tableB = true
  params.dataRange = params.year ? '1' : '0'

  getSubMarketList(params)
  exportMarket(params, 'areaList')
    .then(res => {
      if (res.code == 200) {
        const areaList = res.data.areaList || []
        data.tableB = areaList
        if (data.params.area === '') {
          data.chartData = areaList.map(el => ({ name: el.area, value: el.sales }))
          
        }else{
          isarea.value = false;
        }
      }
      loading.tableB = false
    })
    .catch(e => {
      loading.tableB = false
    })

  exportMarket(params, 'mapTotal')
    .then(res => {
      if (res.code == 200) {
        data.mapTotal.totalProp = res.data.totalProp ? res.data.totalProp : ''
        data.mapTotal.totalSales = res.data.totalSales ? res.data.totalSales : ''
      } else {
        data.mapTotal.totalProp = ''
        data.mapTotal.totalSales = ''
      }
      loading.chartData = false
    })
    .catch(e => {
      loading.chartData = false
    })
  exportMarketDateRange(params).then(res => {
    if (res.code === 200) {
      let minDate = res?.data?.minDate ?? ''
      let maxDate = res?.data?.maxDate ?? ''
      let dateRange = ''
      if (minDate && maxDate && minDate.indexOf('-') !== -1 && maxDate.indexOf('-') !== -1) {
        minDate = minDate.split('-')[1]
        maxDate = maxDate.split('-')[1]
      }
      dateRange = `${minDate}-${maxDate}月`
      if (minDate === maxDate) dateRange = `${minDate}月`
      if (!minDate && !maxDate) dateRange = ''
      data.mapTotal.dateRange = dateRange
    } else {
      data.mapTotal.dateRange = ''
    }
  })
}

const changeSelectArea = ev => {
  data.params.area = ev
  // 重置细分市场
  data.params.finalDestination = '';

  initChartData(data.params)
}

const toggleAreaItem = ev => {
  if (ev === '') return
  // 判断数组对象中是否存在该元素  
  console.log('isarea', data.params.area === '' , isarea.value)
  if (data.params.area === '' && isarea.value) {
    refMaps.value.selectArea(ev)
    data.params.area = ev
   
    initChartData(data.params)
  } else {
    refMaps.value.selectArea(ev)
    data.params.finalDestination = ev
    data.params.area = '';
    loading.tableA = true
    getSubMarketList(data.params)
  }
}
const rowStyle = ({ row }) => {
  return {
    'font-weight': row.area == data.params.finalDestination ? 'bold' : 'normal',
    'font-style': row.area == data.params.finalDestination ? 'italic' : ''
  }
}
// 首次加载请求由头部组件处理好数据后发起
// initChartData(data.params)
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';

.map-wrap {
  position: relative;
}

.sales {
  position: absolute;
  top: 20px;
  left: 30px;
  z-index: 2;
  display: flex;
  &__item {
    position: relative;
    width: 200px;
    height: 100px;
    padding: 20px;
    margin-right: 20px;
    border-radius: 8px;
    border: 1px solid #92cdfc;
    background: linear-gradient(288deg, #c6e0f9 2%, #fafbfc 97%);
    backdrop-filter: blur(13.6px);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
    &--icon {
      position: absolute;
      top: -25px;
      right: -20px;
      width: 100px;
      height: 100px;
    }
    &--name {
      color: #051c2c;
      font-size: 14px;
    }
    &--value {
      color: #0085ff;
      font-size: 36px;
      font-weight: bold;
    }
  }
}
.area-item {
  min-width: 100%;
  cursor: pointer;
}
.search-form {
  :deep(.el-col) {
    margin-bottom: 0;
  }
}
:deep(.el-col) {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
</style>
