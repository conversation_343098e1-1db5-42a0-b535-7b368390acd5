# Tooltip 组件

## 基本用法

## 属性 (Props)

| 参数         | 说明                       | 类型    | 默认值                                                |
| ------------ | -------------------------- | ------- | ----------------------------------------------------- |
| params       | 提示框所需的数据数组       | Array   | []                                                    |
| title        | 提示框的标题               | String  | ''                                                    |
| showTotal    | 是否显示总数信息           | Boolean | false                                                 |
| mapping      | 数据映射对象，用于转换数据 | Object  | { sales: 'sales', proportion: 'slice', yoy: 'slice' } |
| shouldSort   | 是否需要对数据进行排序     | Boolean | true                                                  |
| sortField    | 指定排序字段               | String  | 'proportion'                                          |
| singleColumn | 是否使用单列布局           | Boolean | true                                                  |

## 插槽 (Slots)

| 名称         | 说明                       |
| ------------ | -------------------------- |
| default      | 触发Tooltip显示的内容      |
| hander-right | 标题右侧操作区             |
| item         | 自定义每个数据项的显示内容 |
| total        | 自定义总计信息的显示内容   |

## 事件 (Emits)

| 事件名 | 说明           | 参数 |
| ------ | -------------- | ---- |
| —      | 暂无自定义事件 | —    |

## 实用案例

### 基础用法 (JSX)

```jsx
import { defineComponent } from 'vue'
import Tooltip from '@/views/components/tooltip/index.vue'

export default defineComponent({
  components: { Tooltip },
  setup() {
    const tooltipData = [
      {
        seriesName: '柴油机',
        value: 120,
        color: '#115e93',
        data: { sales: 120, proportion: 40, yoy: 8 }
      },
      {
        seriesName: '汽油机',
        value: 150,
        color: '#36b37e',
        data: { sales: 150, proportion: 50, yoy: 12 }
      },
      {
        seriesName: '其他',
        value: 30,
        color: '#ff8c00',
        data: { sales: 30, proportion: 10, yoy: 3 }
      }
    ]

    return () => (
      <Tooltip params={tooltipData} showTotal={true} singleColumn={false}>
        <div style={{ padding: '8px 16px', border: '1px solid #e8e8e8', borderRadius: '4px' }}>
          悬停查看动力类型销售数据
        </div>
      </Tooltip>
    )
  }
})
```

### 自定义数据项显示 (JSX)

```jsx
import { defineComponent } from 'vue'
import Tooltip from '@/views/components/tooltip/index.vue'

export default defineComponent({
  components: { Tooltip },
  setup() {
    const productData = [
      {
        seriesName: '重型车',
        value: 85,
        color: '#0065ff',
        data: { sales: 85, proportion: 35, yoy: 5 }
      },
      {
        seriesName: '轻型车',
        value: 120,
        color: '#722ed1',
        data: { sales: 120, proportion: 48, yoy: 15 }
      },
      {
        seriesName: '特种车',
        value: 40,
        color: '#f5222d',
        data: { sales: 40, proportion: 17, yoy: -2 }
      }
    ]

    return () => (
      <Tooltip
        params={productData}
        showTotal={true}
        shouldSort={true}
        sortField="sales"
        v-slots={{
          item: ({ item }) => (
            <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <span
                  style={{
                    backgroundColor: item.color,
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    marginRight: '8px'
                  }}
                ></span>
                <span>{item.seriesName}</span>
              </div>
              <div style={{ textAlign: 'right' }}>
                <div>销量: {item.data.sales}台</div>
                <div>
                  同比: {item.data.yoy > 0 ? '+' : ''}
                  {item.data.yoy}%
                </div>
              </div>
            </div>
          )
        }}
      >
        <button
          style={{
            padding: '8px 16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px'
          }}
        >
          查看车型销售详情
        </button>
      </Tooltip>
    )
  }
})
```

### 单列布局与总计自定义 (JSX)

```jsx
import { defineComponent } from 'vue';
import Tooltip from '@/views/components/tooltip/index.vue';

export default defineComponent({
  components: { Tooltip },
  setup() {
    const regionData = [
      { seriesName: '华东地区', value: 210, color: '#13c2c2', data: { sales: 210, proportion: 35, yoy: 10 } },
      { seriesName: '华南地区', value: 180, color: '#00b42a', data: { sales: 180, proportion: 30, yoy: 8 } },
      { seriesName: '华北地区', value: 150, color: '#ff7d00', data: { sales: 150, proportion: 25, yoy: 5 } },
      { seriesName: '西部地区', value: 60, color: '#f5222d', data: { sales: 60, proportion: 10, yoy: 3 } }
    ];

    return () => (
      <Tooltip
        params={regionData}
        showTotal={true}
        singleColumn={true}
        v-slots={{
          total: ({ params }) => (
            <div style={{ marginTop: '12px', paddingTop: '12px', borderTop: '1px dashed #e8e8e8', display: 'flex', justifyContent: 'space-between' }}
              <div style={{ fontWeight: 'bold' }}>全国销售总计</div>
              <div style={{ fontWeight: 'bold', color: '#f5222d' }}>
                {params.reduce((sum, item) => sum + item.data.sales, 0)}台
              </div>
            </div>
          ),
          'hander-right': () => (
            <button style={{ fontSize: '12px', padding: '2px 8px', border: '1px solid #e8e8e8', borderRadius: '4px' }}
              导出数据
            </button>
          )
        }}
      >
        <div style={{ cursor: 'pointer' }}>各区域销售分布</div>
      </Tooltip>
    );
  }
});
```

tooltip中渲染

```js
   :tooltip="{
            formatter: params =>
                  TooltipFormatter(TooltipPercentageComponent, params, {
                    mapping: {
                      sales: 'value',
                      proportion: 'slice',
                      yoy: 'slice'
                    },
                    singleColumn: false,
                    sortField: 'value',
                  })
           }"

```
